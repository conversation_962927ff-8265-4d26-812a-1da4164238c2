# -*- coding: utf-8 -*-
"""
公众号配置管理

作者: AI助手
日期: 2025-07-27
功能: 统一管理公众号信息和采集配置
"""

# 公众号配置信息
公众号配置 = {
    # 投资理财类公众号
    'taotiehai_investment': {
        '名称': '饕餮海投资',
        '描述': '专注跨市场套利、价差套利、可转债套利等投资策略',
        '关键词': ['跨市场套利', '价差套利', '可转债套利', '分红套利', '投资策略'],
        '优先级': 1,
        '是否启用': True,
        '采集频率': '每日',
        '示例链接': [
            'https://mp.weixin.qq.com/s/N2bjVpOHQjbO2cNA0inhAQ'
        ]
    },
    
    'xinye_lowrisk': {
        '名称': '鑫爷低风险投资',
        '描述': '专注低风险套利、稳健投资策略',
        '关键词': ['低风险套利', '稳健投资', '国债逆回购', '银行理财', '货币基金'],
        '优先级': 1,
        '是否启用': True,
        '采集频率': '每日',
        '示例链接': []
    },
    
    # 科技类公众号
    'tech_frontier': {
        '名称': '科技前沿',
        '描述': '人工智能、机器学习等前沿技术资讯',
        '关键词': ['人工智能', '机器学习', '深度学习', '神经网络', '算法优化'],
        '优先级': 2,
        '是否启用': True,
        '采集频率': '每周',
        '示例链接': []
    },
    
    # 商业观察类公众号
    'business_watch': {
        '名称': '商业观察',
        '描述': '商业模式、创业投资、市场分析',
        '关键词': ['商业模式', '创业投资', '市场分析', '企业管理', '商业策略'],
        '优先级': 3,
        '是否启用': True,
        '采集频率': '每周',
        '示例链接': []
    }
}

# 采集器配置
采集器配置 = {
    '保存目录': '数据存储/原文章',
    '每次抓取数量': 5,
    '抓取间隔': 60,  # 分钟
    '请求延迟': 2,   # 秒
    '超时时间': 30,  # 秒
    '重试次数': 3,
    '过滤规则': {
        '最小字数': 500,
        '最大字数': 50000,
        '排除关键词': ['广告', '推广', '测试']
    }
}

# 文件保存配置
文件保存配置 = {
    '格式': 'markdown',
    '包含图片': True,
    '包含链接': True,
    '文件名格式': '{时间戳}_{来源账号}_{标题}',
    '最大文件名长度': 100,
    '编码': 'utf-8'
}

# API配置
API配置 = {
    'gemini': {
        '模型': 'gemini-2.5-flash',
        '环境变量': 'GEMINI_API_KEY',
        '用途': '套利信息提取'
    }
}


def 获取公众号列表(只显示启用=True):
    """
    获取公众号列表
    
    参数:
        只显示启用: 是否只显示启用的公众号
        
    返回:
        公众号列表
    """
    if 只显示启用:
        return {k: v for k, v in 公众号配置.items() if v['是否启用']}
    return 公众号配置


def 获取公众号信息(账号ID):
    """
    获取指定公众号信息
    
    参数:
        账号ID: 公众号ID
        
    返回:
        公众号信息字典
    """
    return 公众号配置.get(账号ID, None)


def 添加公众号(账号ID, 公众号信息):
    """
    添加新的公众号配置
    
    参数:
        账号ID: 公众号ID
        公众号信息: 公众号信息字典
    """
    公众号配置[账号ID] = 公众号信息


def 更新公众号状态(账号ID, 是否启用):
    """
    更新公众号启用状态
    
    参数:
        账号ID: 公众号ID
        是否启用: 是否启用
    """
    if 账号ID in 公众号配置:
        公众号配置[账号ID]['是否启用'] = 是否启用


def 获取采集配置():
    """获取采集器配置"""
    return 采集器配置


def 获取文件保存配置():
    """获取文件保存配置"""
    return 文件保存配置


def 获取API配置():
    """获取API配置"""
    return API配置


def 显示配置信息():
    """显示所有配置信息"""
    print("📋 公众号配置信息")
    print("=" * 50)
    
    for 账号ID, 信息 in 公众号配置.items():
        状态 = "✅ 启用" if 信息['是否启用'] else "❌ 禁用"
        print(f"\n🔸 {信息['名称']} ({账号ID})")
        print(f"   状态: {状态}")
        print(f"   描述: {信息['描述']}")
        print(f"   优先级: {信息['优先级']}")
        print(f"   采集频率: {信息['采集频率']}")
        print(f"   关键词: {', '.join(信息['关键词'])}")
    
    print(f"\n📊 采集器配置")
    print(f"   保存目录: {采集器配置['保存目录']}")
    print(f"   每次抓取数量: {采集器配置['每次抓取数量']}")
    print(f"   抓取间隔: {采集器配置['抓取间隔']} 分钟")
    print(f"   请求延迟: {采集器配置['请求延迟']} 秒")


def 获取示例链接():
    """获取所有示例链接"""
    示例链接列表 = []
    
    for 账号ID, 信息 in 公众号配置.items():
        if 信息['是否启用'] and 信息['示例链接']:
            for 链接 in 信息['示例链接']:
                示例链接列表.append({
                    '链接': 链接,
                    '来源账号': 信息['名称'],
                    '账号ID': 账号ID
                })
    
    return 示例链接列表


if __name__ == "__main__":
    # 显示配置信息
    显示配置信息()
    
    print(f"\n🔗 示例链接:")
    示例链接 = 获取示例链接()
    for i, 链接信息 in enumerate(示例链接, 1):
        print(f"{i}. {链接信息['来源账号']}: {链接信息['链接']}")
